# app/api/api_v1/endpoints/admin_users.py
from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel, EmailStr
from app.services.supabase_service import SupabaseService
from app.middleware.auth_sync_middleware import require_auth

router = APIRouter()

# Dependency pour vérifier les droits super admin
async def require_super_admin(request: Request) -> Dict[str, Any]:
    """Dependency pour exiger un rôle super admin"""
    user = await require_auth(request)
    user_role = user.get("role", "").upper()
    is_superuser = user.get("is_superuser", False)

    if user_role != "SUPER_ADMIN" and not is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Super admin access required"
        )
    return user

# Pydantic models
class AdminUserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str
    role: str
    is_active: bool
    is_verified: bool
    created_at: str
    last_sign_in_at: str = None
    company_role: str
    user_metadata: Dict = None

class AdminUserCreate(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    role: str

class AdminUserUpdate(BaseModel):
    email: EmailStr = None
    first_name: str = None
    last_name: str = None
    role: str = None
    is_active: bool = None

class UserStatusToggle(BaseModel):
    is_active: bool

@router.get("/", response_model=List[AdminUserResponse])
async def list_company_admins(
    request: Request,
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Lister tous les administrateurs d'entreprises (exclut les super admins)
    """
    try:
        supabase_service = SupabaseService()
        users_data = await supabase_service.list_users()

        # Transformer les données Supabase en format AdminUserResponse
        # Exclure les super admins - ils ne doivent pas apparaître dans cette liste
        users = []
        for user in users_data.get("users", []):
            user_role = user.get("user_metadata", {}).get("role", "user").lower()

            # Exclure les super admins de la liste
            if user_role == "super_admin":
                continue

            users.append(AdminUserResponse(
                id=user.get("id"),
                email=user.get("email", ""),
                first_name=user.get("user_metadata", {}).get("first_name", ""),
                last_name=user.get("user_metadata", {}).get("last_name", ""),
                role=user.get("user_metadata", {}).get("role", "user"),
                is_active=not user.get("banned_until"),
                is_verified=bool(user.get("email_confirmed_at")),
                created_at=user.get("created_at", ""),
                last_sign_in_at=user.get("last_sign_in_at"),
                company_role=user.get("user_metadata", {}).get("role", "user"),
                user_metadata=user.get("user_metadata", {})
            ))

        return users
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch users: {str(e)}"
        )

@router.get("/company/{company_id}", response_model=List[AdminUserResponse])
async def list_company_users(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Lister les administrateurs d'une entreprise spécifique
    """
    try:
        from app.models.company import UserCompany
        from app.models.user import User
        from sqlalchemy import select

        print(f"🔍 Recherche des utilisateurs pour l'entreprise {company_id}")

        # Joindre UserCompany avec User pour récupérer les détails
        result = await db.execute(
            select(User, UserCompany)
            .join(UserCompany, User.id == UserCompany.user_id)
            .where(UserCompany.company_id == company_id)
        )
        user_data = result.all()

        print(f"🔍 Trouvé {len(user_data)} utilisateurs pour l'entreprise {company_id}")

        if not user_data:
            return []

        users = []
        for user, user_company in user_data:
            # Exclure les super admins
            if user.role and user.role.upper() == "SUPER_ADMIN":
                print(f"🔍 Exclusion du super admin: {user.email}")
                continue

            print(f"🔍 Ajout de l'utilisateur: {user.email}, rôle: {user.role}")

            users.append(AdminUserResponse(
                id=str(user.id),  # Convertir en string pour compatibilité
                email=user.email or "",
                first_name=user.first_name or "",
                last_name=user.last_name or "",
                role=user.role.value if hasattr(user.role, 'value') else str(user.role) if user.role else "user",
                is_active=user.is_active if user.is_active is not None else True,
                is_verified=user.is_verified if user.is_verified is not None else False,
                created_at=user.created_at.isoformat() if user.created_at else "",
                last_sign_in_at=None,  # Pas disponible dans la base locale
                company_role=user_company.role.value if hasattr(user_company.role, 'value') else str(user_company.role) if user_company.role else "user",
                user_metadata={}
            ))

        print(f"✅ Retour de {len(users)} utilisateurs pour l'entreprise {company_id}")
        return users

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch company users: {str(e)}"
        )

@router.post("/", response_model=AdminUserResponse)
async def create_user(
    user_data: AdminUserCreate,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Créer un nouvel utilisateur (admin seulement)
    """
    try:
        supabase_service = SupabaseService()
        
        user_metadata = {
            "first_name": user_data.first_name,
            "last_name": user_data.last_name,
            "role": user_data.role
        }
        
        result = await supabase_service.create_user(
            email=user_data.email,
            password=user_data.password,
            user_metadata=user_metadata
        )
        
        # Extraire les données utilisateur de la réponse
        if "user" in result:
            user = result["user"]
        else:
            user = result
            
        return AdminUserResponse(
            id=user.get("id"),
            email=user.get("email", ""),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            role=user_data.role,
            is_active=True,
            is_verified=True,
            created_at=user.get("created_at", ""),
            last_sign_in_at=user.get("last_sign_in_at"),
            company_role=user_data.role,
            user_metadata=user.get("user_metadata", {})
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {str(e)}"
        )

@router.put("/{user_id}", response_model=AdminUserResponse)
async def update_user(
    user_id: str,
    user_data: AdminUserUpdate,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Mettre à jour un utilisateur (admin seulement)
    """
    try:
        supabase_service = SupabaseService()
        
        # Préparer les données de mise à jour
        update_data = {}
        user_metadata = {}
        
        if user_data.email:
            update_data["email"] = user_data.email
        if user_data.first_name:
            user_metadata["first_name"] = user_data.first_name
        if user_data.last_name:
            user_metadata["last_name"] = user_data.last_name
        if user_data.role:
            user_metadata["role"] = user_data.role
            
        if user_metadata:
            update_data["user_metadata"] = user_metadata
            
        # Gérer le statut actif/inactif
        if user_data.is_active is not None:
            if user_data.is_active:
                update_data["ban_duration"] = "none"
            else:
                update_data["ban_duration"] = "876000h"  # Ban for 100 years
        
        result = await supabase_service.update_user(user_id, **update_data)
        
        # Extraire les données utilisateur de la réponse
        if "user" in result:
            user = result["user"]
        else:
            user = result
            
        return AdminUserResponse(
            id=user.get("id"),
            email=user.get("email", ""),
            first_name=user.get("user_metadata", {}).get("first_name", ""),
            last_name=user.get("user_metadata", {}).get("last_name", ""),
            role=user.get("user_metadata", {}).get("role", "user"),
            is_active=not user.get("banned_until"),
            is_verified=bool(user.get("email_confirmed_at")),
            created_at=user.get("created_at", ""),
            last_sign_in_at=user.get("last_sign_in_at"),
            company_role=user.get("user_metadata", {}).get("role", "user"),
            user_metadata=user.get("user_metadata", {})
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user: {str(e)}"
        )

@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Supprimer un utilisateur (admin seulement)
    """
    try:
        supabase_service = SupabaseService()
        await supabase_service.delete_user(user_id)
        return {"success": True, "message": "User deleted successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete user: {str(e)}"
        )

@router.post("/{user_id}/toggle-status", response_model=AdminUserResponse)
async def toggle_user_status(
    user_id: str,
    status_data: UserStatusToggle,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Activer/désactiver un utilisateur (admin seulement)
    """
    try:
        supabase_service = SupabaseService()
        
        # Préparer les données de mise à jour pour le statut
        update_data = {}
        if status_data.is_active:
            update_data["ban_duration"] = "none"
        else:
            update_data["ban_duration"] = "876000h"  # Ban for 100 years
        
        result = await supabase_service.update_user(user_id, **update_data)
        
        # Extraire les données utilisateur de la réponse
        if "user" in result:
            user = result["user"]
        else:
            user = result
            
        return AdminUserResponse(
            id=user.get("id"),
            email=user.get("email", ""),
            first_name=user.get("user_metadata", {}).get("first_name", ""),
            last_name=user.get("user_metadata", {}).get("last_name", ""),
            role=user.get("user_metadata", {}).get("role", "user"),
            is_active=not user.get("banned_until"),
            is_verified=bool(user.get("email_confirmed_at")),
            created_at=user.get("created_at", ""),
            last_sign_in_at=user.get("last_sign_in_at"),
            company_role=user.get("user_metadata", {}).get("role", "user"),
            user_metadata=user.get("user_metadata", {})
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle user status: {str(e)}"
        )
