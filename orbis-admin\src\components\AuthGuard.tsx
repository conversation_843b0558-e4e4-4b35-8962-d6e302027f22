'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/fast-auth'

interface AuthGuardProps {
  children: React.ReactNode
  requireSuperAdmin?: boolean
}

export default function AuthGuard({ children, requireSuperAdmin = false }: AuthGuardProps) {
  const { isAuthenticated, loading, user, isSuperAdmin } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        console.log('🔒 Non authentifié, redirection vers /login')
        router.push('/login')
        return
      }

      if (requireSuperAdmin && !isSuperAdmin) {
        console.log('🔒 Super admin requis, accès refusé')
        router.push('/unauthorized')
        return
      }
    }
  }, [loading, isAuthenticated, isSuperAdmin, requireSuperAdmin, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification de l'authentification...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // La redirection est gérée dans useEffect
  }

  if (requireSuperAdmin && !isSuperAdmin) {
    return null // La redirection est gérée dans useEffect
  }

  return <>{children}</>
}
