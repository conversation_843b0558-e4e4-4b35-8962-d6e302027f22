#!/usr/bin/env python3
"""
Test du CRUD complet des administrateurs
"""

import requests
import json

API_BASE = "http://localhost:8000"

def get_auth_token():
    """Obtenir un token d'authentification"""
    response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        raise Exception(f"Login failed: {response.text}")

def get_test_company_id(headers):
    """Récupérer l'ID d'une entreprise de test"""
    response = requests.get(f"{API_BASE}/api/v1/admin/companies/", headers=headers)
    if response.status_code == 200:
        companies = response.json()
        if companies:
            return companies[0]['id']  # Prendre la première entreprise
    return None

def test_admin_crud():
    """Test CRUD complet des administrateurs"""
    print("👥 Test CRUD Administrateurs\n")
    
    # Obtenir le token
    token = get_auth_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Obtenir une entreprise de test
    company_id = get_test_company_id(headers)
    if not company_id:
        print("❌ Aucune entreprise trouvée pour les tests")
        return False
    
    print(f"🏢 Utilisation de l'entreprise ID: {company_id}")
    
    # 1. READ - Lister les administrateurs de l'entreprise (avant création)
    print("\n1. 📖 Test READ (liste initiale)...")
    response = requests.get(f"{API_BASE}/api/v1/admin/users/company/{company_id}", headers=headers)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        initial_admins = response.json()
        print(f"✅ READ réussi - {len(initial_admins)} administrateurs initiaux")
    else:
        print(f"⚠️ READ - Pas d'administrateurs ou erreur: {response.text}")
        initial_admins = []
    
    # 2. CREATE - Créer un nouvel administrateur
    print("\n2. 🆕 Test CREATE...")
    new_admin_data = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "first_name": "Admin",
        "last_name": "Test",
        "role": "ADMIN",
        "company_id": company_id
    }
    
    response = requests.post(f"{API_BASE}/api/v1/admin/users/", 
                           headers=headers, json=new_admin_data)
    
    if response.status_code == 200:
        created_admin = response.json()
        admin_id = created_admin['id']
        print(f"✅ CREATE réussi - ID: {admin_id}, Email: {created_admin['email']}")
    else:
        print(f"❌ CREATE échoué: {response.text}")
        return False
    
    # 3. READ - Vérifier que l'administrateur est dans la liste
    print("\n3. 📖 Test READ (après création)...")
    response = requests.get(f"{API_BASE}/api/v1/admin/users/company/{company_id}", headers=headers)
    
    if response.status_code == 200:
        admins = response.json()
        print(f"✅ READ réussi - {len(admins)} administrateurs trouvés")
        
        # Vérifier que notre admin est dans la liste
        found = any(a['id'] == admin_id for a in admins)
        if found:
            print(f"✅ Administrateur créé trouvé dans la liste")
        else:
            print(f"❌ Administrateur créé non trouvé dans la liste")
    else:
        print(f"❌ READ échoué: {response.text}")
        return False
    
    # 4. UPDATE - Modifier l'administrateur
    print("\n4. ✏️ Test UPDATE...")
    update_data = {
        "first_name": "Admin Modifié",
        "last_name": "Test Updated",
        "role": "MANAGER"
    }
    
    response = requests.put(f"{API_BASE}/api/v1/admin/users/{admin_id}", 
                          headers=headers, json=update_data)
    
    if response.status_code == 200:
        updated_admin = response.json()
        print(f"✅ UPDATE réussi - Nouveau nom: {updated_admin['first_name']} {updated_admin['last_name']}")
        print(f"✅ Nouveau rôle: {updated_admin['role']}")
    else:
        print(f"❌ UPDATE échoué: {response.text}")
        return False
    
    # 5. TOGGLE STATUS - Changer le statut
    print("\n5. 🔄 Test TOGGLE STATUS...")
    response = requests.post(f"{API_BASE}/api/v1/admin/users/{admin_id}/toggle-status", 
                           headers=headers)
    
    if response.status_code == 200:
        toggled_admin = response.json()
        print(f"✅ TOGGLE STATUS réussi - Statut: {'Actif' if toggled_admin['is_active'] else 'Inactif'}")
    else:
        print(f"❌ TOGGLE STATUS échoué: {response.text}")
        return False
    
    # 6. DELETE - Supprimer l'administrateur
    print("\n6. 🗑️ Test DELETE...")
    response = requests.delete(f"{API_BASE}/api/v1/admin/users/{admin_id}", 
                             headers=headers)
    
    if response.status_code == 200:
        print(f"✅ DELETE réussi - Administrateur supprimé")
    else:
        print(f"❌ DELETE échoué: {response.text}")
        return False
    
    # 7. Vérifier que l'administrateur n'existe plus
    print("\n7. 🔍 Vérification suppression...")
    response = requests.get(f"{API_BASE}/api/v1/admin/users/company/{company_id}", headers=headers)
    
    if response.status_code == 200:
        final_admins = response.json()
        found = any(a['id'] == admin_id for a in final_admins)
        if not found:
            print(f"✅ Vérification réussie - Administrateur bien supprimé")
            print(f"✅ Retour à {len(final_admins)} administrateurs (comme initialement: {len(initial_admins)})")
        else:
            print(f"❌ Vérification échouée - Administrateur encore présent")
            return False
    else:
        print(f"❌ Vérification échouée: {response.text}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Test CRUD complet des administrateurs\n")
    
    try:
        success = test_admin_crud()
        
        if success:
            print("\n🎉 Tous les tests CRUD administrateurs ont réussi !")
            print("✅ CREATE, READ, UPDATE, TOGGLE STATUS, DELETE fonctionnent")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur lors des tests: {e}")
    
    print("\n✅ Tests terminés")
