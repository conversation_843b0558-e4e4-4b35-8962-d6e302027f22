#!/usr/bin/env python3
"""
Test du système d'authentification hybride
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_login():
    """Test de connexion avec FastAuth"""
    print("🔐 Test de connexion...")
    
    response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Connexion réussie")
        print(f"Token: {data['access_token'][:50]}...")
        print(f"User: {data['user']['email']}")
        print(f"Role: {data['user']['role']}")
        print(f"Super Admin: {data['user']['is_superuser']}")
        return data['access_token']
    else:
        print(f"❌ Erreur: {response.text}")
        return None

def test_companies_access(token):
    """Test d'accès aux companies avec le token"""
    print("\n🏢 Test d'accès aux companies...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{API_BASE}/api/v1/admin/companies/", headers=headers)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Accès autorisé - {len(data)} companies trouvées")
        for company in data[:3]:  # Afficher les 3 premières
            print(f"  - {company['name']} ({company['code']})")
    else:
        print(f"❌ Erreur: {response.text}")

def test_token_verify(token):
    """Test de vérification du token"""
    print("\n🔍 Test de vérification du token...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{API_BASE}/api/v1/fast-auth/verify", headers=headers)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Token valide")
        print(f"User: {data['user']['email']}")
    else:
        print(f"❌ Token invalide: {response.text}")

if __name__ == "__main__":
    print("🚀 Test du système d'authentification hybride\n")
    
    # 1. Test de connexion
    token = test_login()
    
    if token:
        # 2. Test de vérification du token
        test_token_verify(token)
        
        # 3. Test d'accès aux companies
        test_companies_access(token)
    
    print("\n✅ Tests terminés")
