'use client'

import { useState, useEffect } from 'react'
import { Building2, Plus, Search, Eye, MapPin, Phone, Mail, Globe, Edit, Trash2, Power } from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastCompanyService } from '@/lib/fast-auth'
import { useRouter } from 'next/navigation'
import AuthGuard from '@/components/AuthGuard'

interface Company {
  id: number
  name: string
  code: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  is_active: boolean
  user_count: number
}

function CompaniesPageContent() {
  const { success, error: showError } = useToast()
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [actionLoading, setActionLoading] = useState<number | null>(null)
  const router = useRouter()

  useEffect(() => {
    loadCompanies()
  }, [])

  const loadCompanies = async () => {
    try {
      setLoading(true)
      console.log('🔄 Chargement des entreprises...')
      const companiesData = await FastCompanyService.getCompanies()
      console.log('✅ Entreprises chargées:', companiesData.length)
      setCompanies(companiesData)
    } catch (error) {
      console.error('❌ Erreur chargement entreprises:', error)
      showError('Erreur', 'Impossible de charger les entreprises')
    } finally {
      setLoading(false)
    }
  }

  const handleCompanyClick = (companyId: number) => {
    router.push(`/companies/${companyId}`)
  }

  const handleNewCompany = () => {
    router.push('/companies/new')
  }

  const handleEditCompany = (e: React.MouseEvent, companyId: number) => {
    e.stopPropagation()
    router.push(`/companies/${companyId}/edit`)
  }

  const handleToggleStatus = async (e: React.MouseEvent, companyId: number) => {
    e.stopPropagation()

    try {
      setActionLoading(companyId)
      console.log('🔄 Changement de statut entreprise:', companyId)

      await FastCompanyService.toggleCompanyStatus(companyId)

      console.log('✅ Statut changé')
      success('Succès', 'Statut de l\'entreprise modifié')

      // Recharger la liste
      await loadCompanies()

    } catch (error) {
      console.error('❌ Erreur changement statut:', error)
      showError('Erreur', error instanceof Error ? error.message : 'Impossible de modifier le statut')
    } finally {
      setActionLoading(null)
    }
  }

  const handleDeleteCompany = async (e: React.MouseEvent, companyId: number, companyName: string) => {
    e.stopPropagation()

    if (!confirm(`Êtes-vous sûr de vouloir supprimer l'entreprise "${companyName}" ?\n\nCette action est irréversible.`)) {
      return
    }

    try {
      setActionLoading(companyId)
      console.log('🔄 Suppression entreprise:', companyId)

      await FastCompanyService.deleteCompany(companyId)

      console.log('✅ Entreprise supprimée')
      success('Succès', 'Entreprise supprimée avec succès')

      // Recharger la liste
      await loadCompanies()

    } catch (error) {
      console.error('❌ Erreur suppression:', error)
      showError('Erreur', error instanceof Error ? error.message : 'Impossible de supprimer l\'entreprise')
    } finally {
      setActionLoading(null)
    }
  }

  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des entreprises...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Building2 className="w-8 h-8 text-green-600" />
            Entreprises
          </h1>
          <p className="text-gray-600 mt-1">
            Gérez les entreprises clientes et leurs administrateurs
          </p>
        </div>
        <button
          onClick={handleNewCompany}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Nouvelle Entreprise
        </button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Rechercher une entreprise..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
      </div>

      {/* Companies Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCompanies.length === 0 ? (
          <div className="col-span-full">
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune entreprise</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? 'Aucune entreprise ne correspond à votre recherche.' : 'Commencez par créer votre première entreprise.'}
              </p>
              {!searchTerm && (
                <button
                  onClick={handleNewCompany}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 mx-auto transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  Créer une entreprise
                </button>
              )}
            </div>
          </div>
        ) : (
          filteredCompanies.map((company) => (
            <div
              key={company.id}
              className="bg-white rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                {/* Company Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <Building2 className="w-6 h-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 text-lg">{company.name}</h3>
                      <p className="text-sm text-gray-600">{company.code}</p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    company.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {company.is_active ? 'Actif' : 'Inactif'}
                  </span>
                </div>

                {/* Company Description */}
                {company.description && (
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {company.description}
                  </p>
                )}

                {/* Company Details */}
                <div className="space-y-2 mb-4">
                  {company.address && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span className="truncate">{company.address}</span>
                    </div>
                  )}
                  {company.phone && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Phone className="w-4 h-4" />
                      <span>{company.phone}</span>
                    </div>
                  )}
                  {company.email && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Mail className="w-4 h-4" />
                      <span className="truncate">{company.email}</span>
                    </div>
                  )}
                  {company.website && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Globe className="w-4 h-4" />
                      <span className="truncate">{company.website}</span>
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>{company.user_count || 0} administrateur(s)</span>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    {/* Bouton Voir */}
                    <button
                      onClick={() => handleCompanyClick(company.id)}
                      className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Voir détails"
                    >
                      <Eye className="w-4 h-4" />
                    </button>

                    {/* Bouton Modifier */}
                    <button
                      onClick={(e) => handleEditCompany(e, company.id)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="Modifier"
                      disabled={actionLoading === company.id}
                    >
                      <Edit className="w-4 h-4" />
                    </button>

                    {/* Bouton Toggle Status */}
                    <button
                      onClick={(e) => handleToggleStatus(e, company.id)}
                      className={`p-2 rounded-lg transition-colors ${
                        company.is_active
                          ? 'text-orange-600 hover:bg-orange-50'
                          : 'text-green-600 hover:bg-green-50'
                      }`}
                      title={company.is_active ? 'Désactiver' : 'Activer'}
                      disabled={actionLoading === company.id}
                    >
                      {actionLoading === company.id ? (
                        <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      ) : (
                        <Power className="w-4 h-4" />
                      )}
                    </button>

                    {/* Bouton Supprimer */}
                    <button
                      onClick={(e) => handleDeleteCompany(e, company.id, company.name)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Supprimer"
                      disabled={actionLoading === company.id}
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default function CompaniesPage() {
  return (
    <AuthGuard requireSuperAdmin={true}>
      <CompaniesPageContent />
    </AuthGuard>
  )
}
